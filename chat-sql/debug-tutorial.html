<!DOCTYPE html>
<html>
<head>
    <title>Debug Tutorial Data</title>
</head>
<body>
    <h1>Debug Tutorial Data</h1>
    <button onclick="clearData()">Clear All Data</button>
    <button onclick="viewData()">View All Data</button>
    <button onclick="testTutorialInit()">Test Tutorial Init</button>
    <div id="output"></div>

    <script>
        // 打开 IndexedDB
        function openDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open('llm_problems_db', 1);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }

        // 清空所有数据
        async function clearData() {
            try {
                const db = await openDB();
                const transaction = db.transaction(['problems'], 'readwrite');
                const store = transaction.objectStore('problems');
                await new Promise((resolve, reject) => {
                    const request = store.clear();
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
                document.getElementById('output').innerHTML = '<p>所有数据已清空</p>';
            } catch (error) {
                document.getElementById('output').innerHTML = '<p>清空失败: ' + error + '</p>';
            }
        }

        // 查看所有数据
        async function viewData() {
            try {
                const db = await openDB();
                const transaction = db.transaction(['problems'], 'readonly');
                const store = transaction.objectStore('problems');
                const request = store.getAll();
                
                request.onsuccess = () => {
                    const data = request.result;
                    let html = '<h2>数据库内容 (' + data.length + ' 条记录):</h2>';
                    data.forEach((item, index) => {
                        html += '<div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">';
                        html += '<h3>记录 ' + (index + 1) + ' (ID: ' + item.id + ')</h3>';
                        html += '<p><strong>Title:</strong> ' + (item.title || 'N/A') + '</p>';
                        html += '<p><strong>isTutorial:</strong> ' + (item.isTutorial || false) + '</p>';
                        html += '<p><strong>createdAt:</strong> ' + item.createdAt + '</p>';
                        html += '<p><strong>Data keys:</strong> ' + Object.keys(item.data || {}).join(', ') + '</p>';
                        if (item.data && item.data.description) {
                            html += '<p><strong>Description:</strong> ' + item.data.description.substring(0, 100) + '...</p>';
                        }
                        html += '</div>';
                    });
                    document.getElementById('output').innerHTML = html;
                };
            } catch (error) {
                document.getElementById('output').innerHTML = '<p>查看失败: ' + error + '</p>';
            }
        }

        // 测试教程初始化逻辑
        function testTutorialInit() {
            // 模拟教程数据
            const mockTutorial = {
                title: "测试教程",
                description: "这是一个测试教程的描述",
                problem: ["问题1", "问题2"],
                hint: "这是提示",
                tags: ["test"],
                tableStructure: [],
                tuples: [],
                expected_result: [],
                data: {
                    isBuiltIn: true,
                    order: 999,
                    category: "test"
                }
            };

            const formattedTutorial = {
                description: mockTutorial.description,
                problem: mockTutorial.problem,
                hint: mockTutorial.hint,
                tags: mockTutorial.tags,
                tableStructure: mockTutorial.tableStructure,
                tuples: mockTutorial.tuples,
                expected_result: mockTutorial.expected_result,
                isBuiltIn: true,
                order: mockTutorial.data.order,
                category: mockTutorial.data.category
            };

            const tutorialRecord = {
                title: mockTutorial.title,
                isTutorial: true,
                createdAt: new Date(),
                data: formattedTutorial
            };

            console.log('测试数据 - formattedTutorial:', formattedTutorial);
            console.log('测试数据 - tutorialRecord:', tutorialRecord);

            document.getElementById('output').innerHTML = 
                '<h2>测试数据已输出到控制台</h2>' +
                '<p>请查看浏览器控制台查看详细信息</p>';
        }
    </script>
</body>
</html>

// 测试教程修复效果的脚本
// 在浏览器控制台中运行

async function testTutorialFix() {
    console.log('开始测试教程修复效果...');
    
    // 1. 清空现有数据
    try {
        const { clearAllProblems } = await import('./src/services/recordsIndexDB.tsx');
        await clearAllProblems();
        console.log('✓ 已清空现有数据');
    } catch (error) {
        console.error('清空数据失败:', error);
    }
    
    // 2. 模拟教程初始化
    try {
        const { useSimpleStorage } = await import('./src/hooks/useRecords.tsx');
        const { storeProblem } = useSimpleStorage();
        
        // 模拟教程数据
        const mockTutorial = {
            title: "测试教程标题",
            description: "这是一个测试教程的详细描述，用于验证修复效果",
            problem: ["问题1: 查询所有数据", "问题2: 过滤特定条件"],
            hint: "这是一个测试提示",
            tags: ["test", "select"],
            tableStructure: [],
            tuples: [],
            expected_result: [],
            data: {
                isBuiltIn: true,
                order: 999,
                category: "test"
            }
        };
        
        // 格式化教程数据（核心数据）
        const coreData = {
            description: mockTutorial.description,
            problem: mockTutorial.problem,
            hint: mockTutorial.hint,
            tags: mockTutorial.tags,
            tableStructure: mockTutorial.tableStructure,
            tuples: mockTutorial.tuples,
            expected_result: mockTutorial.expected_result,
            isBuiltIn: true,
            order: mockTutorial.data.order,
            category: mockTutorial.data.category
        };
        
        // 元数据
        const recordMetadata = {
            title: mockTutorial.title,
            isTutorial: true,
            createdAt: new Date()
        };
        
        console.log('准备保存的数据:');
        console.log('- coreData:', coreData);
        console.log('- recordMetadata:', recordMetadata);
        
        // 保存教程
        const savedId = await storeProblem(coreData, recordMetadata);
        console.log('✓ 教程保存成功，ID:', savedId);
        
        // 3. 验证保存结果
        const { getProblemById } = await import('./src/services/recordsIndexDB.tsx');
        const savedRecord = await getProblemById(savedId);
        
        console.log('保存的记录:', savedRecord);
        
        // 检查关键字段
        const checks = {
            'ID存在': savedRecord?.id === savedId,
            'Title正确': savedRecord?.title === mockTutorial.title,
            'isTutorial正确': savedRecord?.isTutorial === true,
            'Description存在': savedRecord?.data?.description === mockTutorial.description,
            'Problem存在': Array.isArray(savedRecord?.data?.problem) && savedRecord.data.problem.length > 0,
            'Hint存在': savedRecord?.data?.hint === mockTutorial.hint
        };
        
        console.log('验证结果:');
        Object.entries(checks).forEach(([key, value]) => {
            console.log(`${value ? '✓' : '✗'} ${key}: ${value}`);
        });
        
        const allPassed = Object.values(checks).every(v => v);
        console.log(allPassed ? '🎉 所有测试通过！修复成功！' : '❌ 部分测试失败，需要进一步调试');
        
        return { success: allPassed, savedRecord, checks };
        
    } catch (error) {
        console.error('测试过程中出错:', error);
        return { success: false, error };
    }
}

// 导出测试函数
window.testTutorialFix = testTutorialFix;
console.log('测试函数已加载，请在控制台运行: testTutorialFix()');

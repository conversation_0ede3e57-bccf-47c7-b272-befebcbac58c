'use client'

import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Divider } from 'antd';
import { tutorials } from '@/components/Tutorial/tutorialData';
import { useSimpleStorage } from '@/hooks/useRecords';
import { LLMProblem, clearAllProblems, getProblemById } from '@/services/recordsIndexDB';

const { Title, Text, Paragraph } = Typography;

export default function TestTutorialPage() {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { storeProblem } = useSimpleStorage();

  const runTest = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      console.log('开始测试教程修复效果...');
      
      // 1. 清空现有数据
      await clearAllProblems();
      console.log('✓ 已清空现有数据');
      
      // 2. 使用第一个教程进行测试
      const testTutorial = tutorials[0];
      console.log('测试教程:', testTutorial.title);
      
      // 3. 格式化教程数据（核心数据）
      const coreData = {
        description: testTutorial.description,
        problem: testTutorial.problem,
        hint: testTutorial.hint,
        tags: testTutorial.tags,
        tableStructure: testTutorial.tableStructure,
        tuples: testTutorial.tuples,
        expected_result: testTutorial.expected_result,
        isBuiltIn: true,
        order: testTutorial.data.order,
        category: testTutorial.data.category
      };
      
      // 4. 元数据
      const recordMetadata: Partial<LLMProblem> = {
        title: testTutorial.title,
        isTutorial: true,
        createdAt: new Date()
      };
      
      console.log('准备保存的数据:');
      console.log('- coreData:', coreData);
      console.log('- recordMetadata:', recordMetadata);
      
      // 5. 保存教程
      const savedId = await storeProblem(coreData, recordMetadata);
      console.log('✓ 教程保存成功，ID:', savedId);
      
      // 6. 验证保存结果
      const savedRecord = await getProblemById(savedId);
      console.log('保存的记录:', savedRecord);
      
      // 7. 检查关键字段
      const checks = {
        'ID存在': savedRecord?.id === savedId,
        'Title正确': savedRecord?.title === testTutorial.title,
        'isTutorial正确': savedRecord?.isTutorial === true,
        'Description存在': savedRecord?.data?.description === testTutorial.description,
        'Problem存在': Array.isArray(savedRecord?.data?.problem) && savedRecord.data.problem.length > 0,
        'Hint存在': savedRecord?.data?.hint === testTutorial.hint,
        'Tags存在': Array.isArray(savedRecord?.data?.tags) && savedRecord.data.tags.length > 0
      };
      
      console.log('验证结果:');
      Object.entries(checks).forEach(([key, value]) => {
        console.log(`${value ? '✓' : '✗'} ${key}: ${value}`);
      });
      
      const allPassed = Object.values(checks).every(v => v);
      console.log(allPassed ? '🎉 所有测试通过！修复成功！' : '❌ 部分测试失败，需要进一步调试');
      
      setTestResult({
        success: allPassed,
        savedRecord,
        checks,
        originalTutorial: testTutorial,
        coreData,
        recordMetadata
      });
      
    } catch (error) {
      console.error('测试过程中出错:', error);
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>教程修复效果测试</Title>
      
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text>此页面用于测试教程记录保存修复效果。点击下方按钮开始测试。</Text>
          </div>
          
          <Button 
            type="primary" 
            size="large" 
            onClick={runTest}
            loading={isLoading}
          >
            开始测试
          </Button>
          
          {testResult && (
            <>
              <Divider />
              <div>
                <Title level={4}>
                  测试结果: {testResult.success ? '✅ 成功' : '❌ 失败'}
                </Title>
                
                {testResult.error && (
                  <Card type="inner" title="错误信息">
                    <Text code>{testResult.error}</Text>
                  </Card>
                )}
                
                {testResult.checks && (
                  <Card type="inner" title="验证结果">
                    {Object.entries(testResult.checks).map(([key, value]) => (
                      <div key={key}>
                        <Text>{value ? '✅' : '❌'} {key}: {String(value)}</Text>
                      </div>
                    ))}
                  </Card>
                )}
                
                {testResult.savedRecord && (
                  <Card type="inner" title="保存的记录">
                    <Paragraph>
                      <Text strong>ID:</Text> {testResult.savedRecord.id}<br/>
                      <Text strong>Title:</Text> {testResult.savedRecord.title}<br/>
                      <Text strong>isTutorial:</Text> {String(testResult.savedRecord.isTutorial)}<br/>
                      <Text strong>Description:</Text> {testResult.savedRecord.data?.description?.substring(0, 100)}...<br/>
                      <Text strong>Problem Count:</Text> {testResult.savedRecord.data?.problem?.length}<br/>
                      <Text strong>Hint:</Text> {testResult.savedRecord.data?.hint?.substring(0, 50)}...
                    </Paragraph>
                  </Card>
                )}
              </div>
            </>
          )}
        </Space>
      </Card>
    </div>
  );
}
